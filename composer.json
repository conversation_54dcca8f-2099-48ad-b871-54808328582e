{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "ext-curl": "*", "ext-zip": "*", "abordage/nova-total-card": "^0.2.1", "akbsit/nova-card-cache": "^1.0", "alexwenzel/ajax-select": "^1.0", "alexwenzel/nova-dependency-container": "^1.12", "algolia/scout-extended": "^3.0", "barryvdh/laravel-snappy": "^1.0", "blendbyte/nova-attach-many": "^1.7.0", "bugsnag/bugsnag-laravel": "^2.0", "campaignmonitor/createsend-php": "^7.1", "capitalc/allfilters": "*", "capitalc/anchor": "@dev", "capitalc/categories": "*", "capitalc/check-pos": "*", "capitalc/check_gc": "*", "capitalc/checkbox": "*", "capitalc/comboproducts": "*", "capitalc/creators": "*", "capitalc/currencyfield": "*", "capitalc/custom-csv-import": "*", "capitalc/department-menu": "*", "capitalc/filteritem": "*", "capitalc/filters": "*", "capitalc/froala": "*", "capitalc/general-search": "*", "capitalc/internation-price-duration": "*", "capitalc/link": "*", "capitalc/menutrack": "*", "capitalc/order-actions-view": "@dev", "capitalc/order-customer-view": "@dev", "capitalc/order-detail-view": "@dev", "capitalc/order-pos-view": "@dev", "capitalc/order-pricing-view": "@dev", "capitalc/order-products-view": "@dev", "capitalc/paths": "*", "capitalc/price-duration": "*", "capitalc/product-search": "*", "capitalc/recurring-info": "*", "capitalc/report": "*", "capitalc/reruns": "*", "capitalc/s3-upload": "*", "capitalc/select2": "*", "capitalc/sidebar-navigation": "@dev", "capitalc/static-text": "@dev", "capitalc/variations": "*", "capitalc/wherein-filter": "*", "digital-creative/nova-range-input-filter": "^1.1", "dillingham/nova-button": "^1.0", "doctrine/dbal": "^4.2", "ebess/advanced-nova-media-library": "4.2", "ericlagarda/nova-text-card": "^1.2", "google/apiclient": "^2.15.0", "halaxa/json-machine": "^1.2", "jenssegers/agent": "^2.6", "kalnoy/nestedset": "^6.0", "laravel/framework": "^11.31", "laravel/horizon": "^5.30", "laravel/nova": "^4.0", "laravel/telescope": "^5.2", "laravel/tinker": "^2.9", "laravel/vapor-core": "^2.37", "league/flysystem-aws-s3-v3": "^3.29", "maatwebsite/excel": "^3.1", "maatwebsite/laravel-nova-excel": "^1.3", "mxl/laravel-queue-rate-limit": "^3.5", "optimistdigital/nova-menu-builder": "^7.1", "optimistdigital/nova-sortable": "^3.4", "outl1ne/nova-multiselect-filter": "^4.0", "outl1ne/nova-simple-repeatable": "^2.2", "php-junior/nova-logs": "^1.1", "predis/predis": "^2.3", "rap2hpoutre/fast-excel": "^5.5", "route4me/route4me-php": "^1.3", "simonhamp/laravel-nova-csv-import": "^0.7.2", "spatie/array-to-xml": "^3.3", "spatie/laravel-backup": "^9.1", "spatie/laravel-data": "^4.15", "spatie/laravel-medialibrary": "^11.10", "spatie/laravel-referer": "^1.9", "spatie/laravel-sitemap": "^7.2", "spatie/laravel-tags": "^4.7", "spatie/nova-tags-field": "^4.0", "swooinc/laravel-prerender": "dev-main", "taxjar/taxjar-php": "^2.0", "tightenco/nova-google-analytics": "^4.0", "vink/nova-cache-card": "^1.0", "visual-host/vapor-file-upload": "dev-master", "whitecube/nova-flexible-content": "^1.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6.8", "fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0.1"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Http/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "repositories": [{"type": "composer", "url": "https://nova.laravel.com"}, {"type": "path", "url": "./nova-components/Variations"}, {"type": "path", "url": "./nova-components/Checkbox"}, {"type": "path", "url": "./nova-components/Categories"}, {"type": "path", "url": "./nova-components/Currencyfield"}, {"type": "path", "url": "./nova-components/CustomCsvImport"}, {"type": "path", "url": "./nova-components/Select2"}, {"type": "path", "url": "./nova-components/Filters"}, {"type": "path", "url": "./nova-components/Creators"}, {"type": "path", "url": "./nova-components/Creators"}, {"type": "path", "url": "./nova-components/Paths"}, {"type": "path", "url": "./nova-components/Menutrack"}, {"type": "path", "url": "./nova-components/DepartmentMenu"}, {"type": "path", "url": "./nova-components/ProductSearch"}, {"type": "path", "url": "./nova-components/GeneralSearch"}, {"type": "path", "url": "./nova-components/Filteritem"}, {"type": "path", "url": "./nova-components/PriceDuration"}, {"type": "path", "url": "./nova-components/InternationPriceDuration"}, {"type": "path", "url": "./nova-components/Link"}, {"type": "path", "url": "./nova-components/S3Upload"}, {"type": "path", "url": "./nova-components/Allfilters"}, {"type": "path", "url": "./nova-components/RecurringInfo"}, {"type": "path", "url": "./nova-components/Reruns"}, {"type": "path", "url": "./nova-components/Comboproducts"}, {"type": "path", "url": "./nova-components/WhereinFilter"}, {"type": "path", "url": "./nova-components/Froala"}, {"type": "path", "url": "./nova-components/CheckPos"}, {"type": "path", "url": "./nova-components/CheckGc"}, {"type": "path", "url": "./nova-components/SidebarNavigation"}, {"type": "path", "url": "./nova-components/Anchor"}, {"type": "path", "url": "./nova-components/OrderProductsView"}, {"type": "path", "url": "./nova-components/OrderCustomerView"}, {"type": "path", "url": "./nova-components/OrderPricingView"}, {"type": "path", "url": "./nova-components/OrderDetailView"}, {"type": "path", "url": "./nova-components/StaticText"}, {"type": "path", "url": "./nova-components/OrderPosView"}, {"type": "path", "url": "./nova-components/OrderActionsView"}, {"type": "path", "url": "./nova-components/Report"}]}