<?php

namespace Capitalc\CustomCsvImport;

use Illuminate\Support\ServiceProvider;
use Capitalc\CustomCsvImport\Http\Controllers\CustomImportController;

class ToolServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        $this->mergeConfigFrom(__DIR__.'/../config/custom-csv-import.php', 'custom-csv-import');

        if ($this->app->runningInConsole()) {
            $this->publishes([
                __DIR__.'/../config/custom-csv-import.php' => config_path('custom-csv-import.php'),
            ], 'custom-csv-import-config');
        }

        // Override the binding after the original service provider has registered
        $this->app->booted(function () {
            $this->app->when(\SimonHamp\LaravelNovaCsvImport\Http\Controllers\ImportController::class)
                ->needs(\Maatwebsite\Excel\Concerns\ToModel::class)
                ->give(function () {
                    return $this->app->make(CustomImporter::class);
                });

            $this->app->when(\SimonHamp\LaravelNovaCsvImport\Http\Controllers\UploadController::class)
                ->needs(\Maatwebsite\Excel\Concerns\ToModel::class)
                ->give(function () {
                    return $this->app->make(CustomImporter::class);
                });
        });
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        // Register our CustomImporter in the container
        $this->app->singleton(CustomImporter::class, function ($app) {
            return new CustomImporter();
        });
    }
}
