(()=>{"use strict";var e={72:(e,t,r)=>{var o,n=function(){return void 0===o&&(o=Boolean(window&&document&&document.all&&!window.atob)),o},l=function(){var e={};return function(t){if(void 0===e[t]){var r=document.querySelector(t);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(e){r=null}e[t]=r}return e[t]}}(),a=[];function s(e){for(var t=-1,r=0;r<a.length;r++)if(a[r].identifier===e){t=r;break}return t}function i(e,t){for(var r={},o=[],n=0;n<e.length;n++){var l=e[n],i=t.base?l[0]+t.base:l[0],c=r[i]||0,u="".concat(i," ").concat(c);r[i]=c+1;var d=s(u),p={css:l[1],media:l[2],sourceMap:l[3]};-1!==d?(a[d].references++,a[d].updater(p)):a.push({identifier:u,updater:h(p,t),references:1}),o.push(u)}return o}function c(e){var t=document.createElement("style"),o=e.attributes||{};if(void 0===o.nonce){var n=r.nc;n&&(o.nonce=n)}if(Object.keys(o).forEach(function(e){t.setAttribute(e,o[e])}),"function"==typeof e.insert)e.insert(t);else{var a=l(e.insert||"head");if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");a.appendChild(t)}return t}var u,d=(u=[],function(e,t){return u[e]=t,u.filter(Boolean).join("\n")});function p(e,t,r,o){var n=r?"":o.media?"@media ".concat(o.media," {").concat(o.css,"}"):o.css;if(e.styleSheet)e.styleSheet.cssText=d(t,n);else{var l=document.createTextNode(n),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(l,a[t]):e.appendChild(l)}}function m(e,t,r){var o=r.css,n=r.media,l=r.sourceMap;if(n?e.setAttribute("media",n):e.removeAttribute("media"),l&&"undefined"!=typeof btoa&&(o+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(l))))," */")),e.styleSheet)e.styleSheet.cssText=o;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(o))}}var f=null,v=0;function h(e,t){var r,o,n;if(t.singleton){var l=v++;r=f||(f=c(t)),o=p.bind(null,r,l,!1),n=p.bind(null,r,l,!0)}else r=c(t),o=m.bind(null,r,t),n=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(r)};return o(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;o(e=t)}else n()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=n());var r=i(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var o=0;o<r.length;o++){var n=s(r[o]);a[n].references--}for(var l=i(e,t),c=0;c<r.length;c++){var u=s(r[c]);0===a[u].references&&(a[u].updater(),a.splice(u,1))}r=l}}}},262:(e,t)=>{t.A=(e,t)=>{const r=e.__vccOpts||e;for(const[e,o]of t)r[e]=o;return r}},314:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var r=e(t);return t[2]?"@media ".concat(t[2]," {").concat(r,"}"):r}).join("")},t.i=function(e,r,o){"string"==typeof e&&(e=[[null,e,""]]);var n={};if(o)for(var l=0;l<this.length;l++){var a=this[l][0];null!=a&&(n[a]=!0)}for(var s=0;s<e.length;s++){var i=[].concat(e[s]);o&&n[i[0]]||(r&&(i[2]?i[2]="".concat(r," and ").concat(i[2]):i[2]=r),t.push(i))}},t}},637:(e,t,r)=>{r.d(t,{A:()=>l});var o=r(314),n=r.n(o)()(function(e){return e[1]});n.push([e.id,".animate-spin[data-v-70856947]{animation:spin-70856947 1s linear infinite}@keyframes spin-70856947{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}",""]);const l=n}},t={};function r(o){var n=t[o];if(void 0!==n)return n.exports;var l=t[o]={id:o,exports:{}};return e[o](l,l.exports,r),l.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.nc=void 0;const o=Vue;var n={cellpadding:"10"},l={class:"border-b"},a={class:"border-t flex justify-between",style:{"padding-top":"1rem"}},s={class:"flex space-x-4"},i={class:"flex space-x-4"},c={key:0,class:"flex items-center space-x-2"},u={key:1,class:"flex items-center space-x-2"};const d={props:{rows:{type:Array,required:!0},columns:{type:Array,required:!0},file:{type:String,required:!0}},data:function(){return{importing:!1}},methods:{runImport:function(){var e=this;this.importing=!0;var t={file:this.file,resource:this.$page.props.resource,attribute_map:this.$page.props.attribute_map||{},meta:this.$page.props.meta||{},random_string_settings:this.$page.props.random_string_settings||{},custom_values:this.$page.props.custom_values||{},combined_values:this.$page.props.combined_values||{}};Nova.request().post(this.url("import"),t).then(function(t){200===t.status&&(Nova.success("Import job queued successfully! Redirecting to results page..."),setTimeout(function(){Nova.visit("/csv-import/review/"+e.file)},1e3))}).catch(function(t){e.importing=!1,console.error("Import error:",t),t.response&&t.response.data&&t.response.data.error?Nova.error("Import failed: "+t.response.data.error):Nova.error("There was a problem queueing your import. Please try again.")})},reconfigure:function(){Nova.visit("/csv-import/configure/"+this.file)},url:function(e){return"/nova-vendor/laravel-nova-csv-import/"+e}}};var p=r(72),m=r.n(p),f=r(637),v={insert:"head",singleton:!1};m()(f.A,v);f.A.locals;var h=r(262);const g=(0,h.A)(d,[["render",function(e,t,r,d,p,m){var f=(0,o.resolveComponent)("Head"),v=(0,o.resolveComponent)("heading"),h=(0,o.resolveComponent)("HeroiconsOutlineRewind"),g=(0,o.resolveComponent)("LinkButton"),N=(0,o.resolveComponent)("HeroiconsOutlinePlay"),w=(0,o.resolveComponent)("BasicButton"),V=(0,o.resolveComponent)("card");return(0,o.openBlock)(),(0,o.createElementBlock)("div",null,[(0,o.createVNode)(f,null,{default:(0,o.withCtx)(function(){return t[0]||(t[0]=[(0,o.createElementVNode)("title",null,"Preview Import",-1)])}),_:1,__:[0]}),(0,o.createVNode)(v,{class:"mb-6"},{default:(0,o.withCtx)(function(){return t[1]||(t[1]=[(0,o.createTextVNode)("CSV Import - Preview",-1)])}),_:1,__:[1]}),(0,o.createVNode)(V,{class:"p-8 space-y-4",style:{"min-height":"300px"}},{default:(0,o.withCtx)(function(){return[t[5]||(t[5]=(0,o.createElementVNode)("p",null,[(0,o.createTextVNode)(" Here's a preview of the first few rows of your import. If everything looks good, click "),(0,o.createElementVNode)("b",null,"Import"),(0,o.createTextVNode)(" to queue the import job. ")],-1)),(0,o.createElementVNode)("table",n,[(0,o.createElementVNode)("thead",l,[(0,o.createElementVNode)("tr",null,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(r.columns,function(e,t){return(0,o.openBlock)(),(0,o.createElementBlock)("th",{key:t},(0,o.toDisplayString)(e),1)}),128))])]),(0,o.createElementVNode)("tbody",null,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(r.rows,function(e,t){return(0,o.openBlock)(),(0,o.createElementBlock)("tr",{key:t,class:"border-b"},[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(r.columns,function(t,r){return(0,o.openBlock)(),(0,o.createElementBlock)("td",{key:r},(0,o.toDisplayString)(e[t]),1)}),128))])}),128))])]),(0,o.createElementVNode)("div",a,[(0,o.createElementVNode)("div",s,[(0,o.createVNode)(g,{onClick:m.reconfigure},{default:(0,o.withCtx)(function(){return[(0,o.createVNode)(h),t[2]||(t[2]=(0,o.createTextVNode)(" Reconfigure",-1))]}),_:1,__:[2]},8,["onClick"])]),(0,o.createElementVNode)("div",i,[(0,o.createVNode)(w,{onClick:m.runImport,disabled:p.importing,class:"bg-green-600 hover:bg-green-700 text-white"},{default:(0,o.withCtx)(function(){return[p.importing?((0,o.openBlock)(),(0,o.createElementBlock)("span",c,t[3]||(t[3]=[(0,o.createElementVNode)("div",{class:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"},null,-1),(0,o.createElementVNode)("span",null,"Queueing Import...",-1)]))):((0,o.openBlock)(),(0,o.createElementBlock)("span",u,[(0,o.createVNode)(N),t[4]||(t[4]=(0,o.createElementVNode)("span",null,"Import",-1))]))]}),_:1},8,["onClick","disabled"])])])]}),_:1,__:[5]})])}],["__scopeId","data-v-70856947"]]);var N={key:0,class:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg"},w={class:"flex items-center space-x-3"},V={class:"text-lg font-medium text-blue-900"},k={class:"text-blue-700"},E={key:0,class:"mt-2"},b={class:"bg-blue-200 rounded-full h-2"},y={class:"text-sm text-blue-600 mt-1"},x={key:1,class:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg"},C={key:2},_={class:"mb-4 p-4 bg-green-50 border border-green-200 rounded-lg"},B={class:"flex items-center space-x-3"},S={class:"text-green-700"},I={key:0},D={cellpadding:"10"},P={valign:"top",align:"right"},T={valign:"top"},F={valign:"top"},R={key:0},j={valign:"top"},q={valign:"top"},A={key:0},H={key:0},L={cellpadding:"10"},M={class:"border-t flex justify-between",style:{"padding-top":"1rem"}};const O={props:{file:{type:String,required:!0}},data:function(){return{showFailureData:{},showFailures:!1,showErrors:!1,status:"queued",total_rows:0,processed_rows:0,successful_rows:0,failed_rows:0,failures:[],errors:[],pollInterval:null,pollCount:0,maxPollAttempts:720}},computed:{isProcessing:function(){return["queued","processing"].includes(this.status)},progressPercentage:function(){return this.total_rows>0?Math.round(this.processed_rows/this.total_rows*100):0},progressText:function(){return"queued"===this.status?"Your import has been queued and will begin processing shortly...":0===this.total_rows?"Initializing import...":"Processing ".concat(this.processed_rows," of ").concat(this.total_rows," rows...")}},mounted:function(){this.startPolling()},beforeUnmount:function(){this.stopPolling()},methods:{startPolling:function(){var e=this;this.fetchResults(),this.pollInterval=setInterval(function(){if(e.pollCount++,e.pollCount>=e.maxPollAttempts)return e.stopPolling(),void Nova.error("Import polling timed out. Please refresh the page to check status.");e.isProcessing?e.fetchResults():e.stopPolling()},5e3)},stopPolling:function(){this.pollInterval&&(clearInterval(this.pollInterval),this.pollInterval=null)},fetchResults:function(){var e=this;Nova.request().get("/nova-vendor/laravel-nova-csv-import/results/".concat(this.file)).then(function(t){var r=t.data;e.status=r.status,e.total_rows=r.total_rows,e.processed_rows=r.processed_rows,e.successful_rows=r.successful_rows,e.failed_rows=r.failed_rows,e.failures=r.failures||[],e.errors=r.errors||[],e.isProcessing||(e.stopPolling(),"completed"===e.status?Nova.success("Import completed! ".concat(e.successful_rows," rows imported successfully.")):"failed"===e.status&&Nova.error("Import failed. Please check the error details above."))}).catch(function(t){console.error("Error fetching results:",t),e.pollCount>=10&&(e.stopPolling(),Nova.error("Unable to fetch import results. Please refresh the page."))})},reconfigure:function(){this.stopPolling(),Nova.visit("/csv-import/configure/"+this.file)},restart:function(){this.stopPolling(),Nova.visit("/csv-import")}}},$=(0,h.A)(O,[["render",function(e,t,r,n,l,a){var s=(0,o.resolveComponent)("Head"),i=(0,o.resolveComponent)("heading"),c=(0,o.resolveComponent)("BasicButton"),u=(0,o.resolveComponent)("HeroiconsOutlineRewind"),d=(0,o.resolveComponent)("LinkButton"),p=(0,o.resolveComponent)("HeroiconsOutlineRefresh"),m=(0,o.resolveComponent)("card");return(0,o.openBlock)(),(0,o.createElementBlock)("div",null,[(0,o.createVNode)(s,null,{default:(0,o.withCtx)(function(){return t[2]||(t[2]=[(0,o.createElementVNode)("title",null,"Review Import",-1)])}),_:1,__:[2]}),(0,o.createVNode)(i,{class:"mb-6"},{default:(0,o.withCtx)(function(){return t[3]||(t[3]=[(0,o.createTextVNode)("CSV Import - Review",-1)])}),_:1,__:[3]}),(0,o.createVNode)(m,{class:"p-8 space-y-4",style:{"min-height":"300px"}},{default:(0,o.withCtx)(function(){return[a.isProcessing?((0,o.openBlock)(),(0,o.createElementBlock)("div",N,[(0,o.createElementVNode)("div",w,[t[4]||(t[4]=(0,o.createElementVNode)("div",{class:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"},null,-1)),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("h3",V,(0,o.toDisplayString)("queued"===l.status?"Import Queued":"Import Processing"),1),(0,o.createElementVNode)("p",k,(0,o.toDisplayString)(a.progressText),1),l.total_rows>0?((0,o.openBlock)(),(0,o.createElementBlock)("div",E,[(0,o.createElementVNode)("div",b,[(0,o.createElementVNode)("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:(0,o.normalizeStyle)({width:a.progressPercentage+"%"})},null,4)]),(0,o.createElementVNode)("p",y,(0,o.toDisplayString)(a.progressPercentage)+"% complete ("+(0,o.toDisplayString)(l.processed_rows)+" of "+(0,o.toDisplayString)(l.total_rows)+" rows) ",1)])):(0,o.createCommentVNode)("",!0)])])])):(0,o.createCommentVNode)("",!0),"failed"===l.status?((0,o.openBlock)(),(0,o.createElementBlock)("div",x,t[5]||(t[5]=[(0,o.createElementVNode)("div",{class:"flex items-center space-x-3"},[(0,o.createElementVNode)("div",{class:"text-red-600"},[(0,o.createElementVNode)("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[(0,o.createElementVNode)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])]),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("h3",{class:"text-lg font-medium text-red-900"},"Import Failed"),(0,o.createElementVNode)("p",{class:"text-red-700"},"The import job encountered an error and could not complete.")])],-1)]))):(0,o.createCommentVNode)("",!0),"completed"===l.status?((0,o.openBlock)(),(0,o.createElementBlock)("div",C,[(0,o.createElementVNode)("div",_,[(0,o.createElementVNode)("div",B,[t[7]||(t[7]=(0,o.createElementVNode)("div",{class:"text-green-600"},[(0,o.createElementVNode)("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[(0,o.createElementVNode)("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),(0,o.createElementVNode)("div",null,[t[6]||(t[6]=(0,o.createElementVNode)("h3",{class:"text-lg font-medium text-green-900"},"Import Completed",-1)),(0,o.createElementVNode)("p",S,[(0,o.createElementVNode)("b",null,(0,o.toDisplayString)(l.successful_rows),1),(0,o.createTextVNode)(" row(s) out of "+(0,o.toDisplayString)(l.total_rows)+" were successfully imported. ",1)])])])])])):(0,o.createCommentVNode)("",!0),0!==l.failures.length?((0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,{key:3},[(0,o.createVNode)(c,{onClick:t[0]||(t[0]=function(e){return l.showFailures=!l.showFailures})},{default:(0,o.withCtx)(function(){return[(0,o.createTextVNode)((0,o.toDisplayString)(l.showFailures?"Hide failures":"Show failures")+" ("+(0,o.toDisplayString)(l.failures.length)+") ",1)]}),_:1}),l.showFailures?((0,o.openBlock)(),(0,o.createElementBlock)("div",I,[(0,o.createElementVNode)("table",D,[t[8]||(t[8]=(0,o.createElementVNode)("thead",{class:"border-b"},[(0,o.createElementVNode)("tr",null,[(0,o.createElementVNode)("th",null,"Row #"),(0,o.createElementVNode)("th",null,"Attribute"),(0,o.createElementVNode)("th",null,"Data"),(0,o.createElementVNode)("th",null,"Details"),(0,o.createElementVNode)("th",null,"Row Data")])],-1)),(0,o.createElementVNode)("tbody",null,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(l.failures,function(e,t){return(0,o.openBlock)(),(0,o.createElementBlock)("tr",{key:t,class:"border-b"},[(0,o.createElementVNode)("td",P,(0,o.toDisplayString)(e.row),1),(0,o.createElementVNode)("td",T,(0,o.toDisplayString)(e.attribute),1),(0,o.createElementVNode)("td",F,[(0,o.createElementVNode)("code",null,[(0,o.createTextVNode)((0,o.toDisplayString)(e.values[e.attribute])+" ",1),e.values[e.attribute]?(0,o.createCommentVNode)("",!0):((0,o.openBlock)(),(0,o.createElementBlock)("i",R,"null"))])]),(0,o.createElementVNode)("td",j,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.errors,function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{key:e},(0,o.toDisplayString)(e),1)}),128))]),(0,o.createElementVNode)("td",q,[(0,o.createVNode)(c,{onClick:function(e){return l.showFailureData[t]=!l.showFailureData[t]}},{default:(0,o.withCtx)(function(){return[(0,o.createTextVNode)((0,o.toDisplayString)(l.showFailureData[t]?"Hide data":"Show all row data"),1)]}),_:2},1032,["onClick"]),(0,o.withDirectives)((0,o.createElementVNode)("div",null,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.values,function(e,t){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{key:t},[(0,o.createTextVNode)((0,o.toDisplayString)(t)+" : ",1),(0,o.createElementVNode)("code",null,[(0,o.createTextVNode)((0,o.toDisplayString)(e),1),e?(0,o.createCommentVNode)("",!0):((0,o.openBlock)(),(0,o.createElementBlock)("i",A,"null"))])])}),128))],512),[[o.vShow,l.showFailureData[t]]])])])}),128))])])])):(0,o.createCommentVNode)("",!0)],64)):(0,o.createCommentVNode)("",!0),0!==l.errors.length?((0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,{key:4},[(0,o.createVNode)(c,{onClick:t[1]||(t[1]=function(e){return l.showErrors=!l.showErrors})},{default:(0,o.withCtx)(function(){return[(0,o.createTextVNode)((0,o.toDisplayString)(l.showErrors?"Hide errors":"Show errors")+" ("+(0,o.toDisplayString)(l.errors.length)+") ",1)]}),_:1}),l.showErrors?((0,o.openBlock)(),(0,o.createElementBlock)("div",H,[(0,o.createElementVNode)("table",L,[t[9]||(t[9]=(0,o.createElementVNode)("thead",{class:"border-b"},[(0,o.createElementVNode)("tr",null,[(0,o.createElementVNode)("th",null,"Details")])],-1)),(0,o.createElementVNode)("tbody",null,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(l.errors,function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("tr",{key:e.message,class:"border-b"},[(0,o.createElementVNode)("td",null,(0,o.toDisplayString)(e.message),1)])}),128))])])])):(0,o.createCommentVNode)("",!0)],64)):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",M,[(0,o.createVNode)(d,{onClick:a.reconfigure},{default:(0,o.withCtx)(function(){return[(0,o.createVNode)(u),t[10]||(t[10]=(0,o.createTextVNode)(" Reconfigure",-1))]}),_:1,__:[10]},8,["onClick"]),(0,o.createVNode)(d,{onClick:a.restart},{default:(0,o.withCtx)(function(){return[(0,o.createVNode)(p),t[11]||(t[11]=(0,o.createTextVNode)(" Upload another",-1))]}),_:1,__:[11]},8,["onClick"])])]}),_:1})])}]]);Nova.booting(function(e,t){e.component("QueuedPreview",g),e.component("QueuedReview",$),Nova.inertia("CsvImport/Preview",g),Nova.inertia("CsvImport/Review",$)})})();