<template>
    <span v-if="loaded">
        <DefaultField
            :field="field"
            :errors="errors"
            :show-help-text="showHelpText"
            :full-width-content="fullWidthContent"
        >
            <template #field>
                <treeselect
                    v-model="value"
                    :async="true"
                    :searchable="true"
                    :options="initialOptions"
                    @select="blurInput"
                    :load-options="loadOptions"
                    :disableBranchNodes="true"
                    :max-height="200"
                    :show-count="true"
                    ref="tree"
                >
                    <div slot="value-label" slot-scope="{ node }">
                        {{ node.raw.name }}
                    </div>
                    <div slot="option-label" slot-scope="{ node }">
                        {{ node.raw.name }}
                    </div>
                </treeselect>
            </template>
        </DefaultField>
        <DefaultField
            v-if="showFilters"
            :field="{
                ...field,
                name: 'Filters',
                indexName: 'filters',
                value: filters,
                attribute:
                    this.field.attribute.split('__')[0] + '__' + 'filter',
            }"
            :errors="errors"
        >
            <template #field>
                <treeselect
                    v-model="filters"
                    :options="filterOptions"
                    :disableBranchNodes="true"
                    :searchable="true"
                    :max-height="200"
                    :show-count="true"
                >
                    <div slot="value-label" slot-scope="{ node }">
                        {{ node.raw.name }}
                    </div>
                    <div slot="option-label" slot-scope="{ node }">
                        {{ node.raw.name }}
                    </div>
                </treeselect>
            </template>
        </DefaultField>
        <DefaultField
            :field="{
                ...field,
                name: 'Tags',
                indexName: 'tags',
                value: tags,
                attribute:
                    this.field.attribute.split('__')[0] + '__' + 'tags',
            }"
            :errors="errors"
        >
            <template #field>
                <treeselect
                    v-model="tags"
                    :options="tagsOptions"
                    multiple
                    :max-height="200"
                    :searchable="true"
                >
                    <div slot="value-label" slot-scope="{ node }">
                        {{ node.raw.name }}
                    </div>
                    <div slot="option-label" slot-scope="{ node }">
                        {{ node.raw.name }}
                    </div>
                </treeselect>
            </template>
        </DefaultField>
        <DefaultField
            :field="{
                ...field,
                name: 'Status',
                indexName: 'status',
                value: status,
                attribute:
                    this.field.attribute.split('__')[0] + '__' + 'status',
            }"
            :errors="errors"
        >
            <template #field>
                <treeselect
                    v-model="status"
                    :options="statuses"
                >
                    <div slot="value-label" slot-scope="{ node }">
                        {{ node.raw.name }}
                    </div>
                    <div slot="option-label" slot-scope="{ node }">
                        {{ node.raw.name }}
                    </div>
                </treeselect>
            </template>
        </DefaultField>
        <DefaultField
            :field="{
                ...field,
                name: 'Path',
                indexName: 'Path',
                value: path,
                attribute:
                    componentId + '__' + 'path',
            }"
            :errors="errors"
        >
            <template #field>
                <input
                    type="text"
                    class="w-full form-control form-input form-input-bordered"
                    :readonly="!editPath"
                    v-model="path" />
            </template>
        </DefaultField>
    </span>
</template>

<script>
import {DependentFormField, HandlesValidationErrors} from 'laravel-nova';
import axios from 'axios';
import Treeselect from "@zanmato/vue3-treeselect";
import "@zanmato/vue3-treeselect/dist/vue3-treeselect.min.css";
import cloneDeep from 'lodash/cloneDeep';
export default {
    mixins: [DependentFormField, HandlesValidationErrors],

    props: ['resourceName', 'resourceId', 'field', 'viaResource'],

    components: { Treeselect },

    data() {
        return {
            componentId: null,
            pageId: null,
            options: null,
            initialOptions: null,
            showFilters: false,
            filters: null,
            filterOptions: null,
            tags: null,
            tagsOptions: null,
            status: null,
            statuses: null,
            path: null,
            loaded: false
        };
    },
    async mounted() {
        console.log(this.field);
        this.options = this.field.options;
        const clonedOptions = cloneDeep(this.options);
        this.initialOptions = clonedOptions.map((parent) => {
            if (parent.children && parent.children.length) {
                return {
                    ...parent,
                    children: parent.children.filter((child) =>
                        child.id === this.value)
                };
            }
            return parent;
        });
        this.tagsOptions = this.field.tags;
        this.statuses = this.field.statuses;
        this.componentId = this.field.attribute.split('__')[0];
        this.pageId = this.resourceId;
        try {
            const response = await axios.get(`/nova-custom-api/pages/${this.pageId}/components/${this.componentId}`);
            if (response.data && response.data.attributes) {
                const data = response.data.attributes;
                this.path = data.path || null;
                this.filters = data.filters ? Number(data.filters) : null;
                this.tags = data.tags_ids ? data.tags_ids.split(',') : [];
                this.tags = this.tags.map((tag) => Number(tag));
                this.status = data.status || null;
            } else {
                console.error('No data found for the component.');
            }
        } catch (error) {
            this.path = null;
            this.filters = null;
            this.tags = [];
            this.status = null;
        } finally {
            this.loaded = true;
        }
    },
    methods: {
        setInitialValue() {
            this.value = this.field.value || '';
        },
        fill(formData) {
            formData.append(this.field.attribute, this.value || '');
            formData.append(this.componentId + '__path', this.path || '');
            formData.append(this.componentId + '__filters', this.filters || '');
            formData.append(this.componentId + '__tags_ids', this.tags || '');
            formData.append(this.componentId + '__status', this.status || '');
        },
        handleChange(value) {
            this.value = value;
        },
        blurInput() {
            if (this.$refs['tree']) {
                this.$refs['tree'].blurInput();
            }
        },
        calculatePath() {
            console.log(this.path)
            if (this.value !== 'custom') {
                this.path = this.value;
                if (this.filters) {
                    const filter = this.filterOptions.find((f) => f.children.find((c) => c.id === this.filters));
                    if (filter) {
                        const value = filter.children.find((c) => c.id === this.filters);
                        if (filter && value) {
                            this.path += '?filters=' + encodeURIComponent(encodeURIComponent(filter.name + '_' + value.name));
                        }
                    }
                }

                if (this.tags && this.tags.length) {
                    const tags = this.tagsOptions.filter((t) => this.tags.includes(t.id))
                        .map((t) => encodeURIComponent(t.label));
                    if (tags.length) {
                        this.path += (this.path.includes('?') ? '&' : '?') + 'tags=' + tags.join(',');
                    }
                }

                if (this.status === 'new') {
                    this.path += (this.path.includes('?') ? '&' : '?') + 'new=true';
                }

                if (this.status === 'sale') {
                    this.path += (this.path.includes('?') ? '&' : '?') + 'sale=true';
                }
            }
        },
        loadOptions({ action, searchQuery, callback }){
            if (action === 'ASYNC_SEARCH') {
                const clonedOptions = cloneDeep(this.options);
                const options = clonedOptions.map((parent) => {
                    if (parent.children && parent.children.length) {
                        const filteredChildren = parent.children.filter((child) =>
                            child.label?.toLowerCase().includes(searchQuery.toLowerCase())
                        ).sort((a, b) => {
                            const aIndex = a.label.toLowerCase().indexOf(searchQuery.toLowerCase());
                            const bIndex = b.label.toLowerCase().indexOf(searchQuery.toLowerCase());
                            return aIndex - bIndex;
                        }).slice(0, 300);
                        return {
                            ...parent,
                            children: filteredChildren
                        };
                    }
                    return parent;
                });
                callback(null, options);
            } else if (action === 'LOAD_ROOT_OPTIONS') {
                const clonedOptions = cloneDeep(this.options);
                const options = clonedOptions.map((parent) => {
                    if (parent.children && parent.children.length) {
                        return {
                            ...parent,
                            children: parent.children.filter((child) =>
                                child.id === this.value)
                        };
                    }
                    return parent;
                });
                callback(null, options);
            } else {
                console.warn('Unknown action:', action);
                callback([]);
            }
        }
    },
    watch: {
        value() {
            if (this.value && this.value.split('/')[1] === 'categories') {
                let id = this.value.split('/').pop();
                axios
                    .get(`/nova-custom-api/categories/${id}/filters`)
                    .then((success) => {
                        if (success.data.length) {
                            this.showFilters = true;
                            this.filterOptions = success.data;
                        } else {
                            this.showFilters = false;
                            this.filterOptions = null;
                            this.filters = null;
                        }
                    })
                    .catch((err) => {});
            } else {
                this.showFilters = false;
                this.filterOptions = null;
                this.filters = null;
                if (this.value === 'custom') {
                    this.path = '';
                }
            }
            this.calculatePath();
        },
        filters() {
            this.calculatePath();
        },
        tags() {
            this.calculatePath();
        },
        status() {
            this.calculatePath();
        },
    },
    computed: {
        editPath() {
            return this.value === 'custom';
        },
    }
};
</script>
