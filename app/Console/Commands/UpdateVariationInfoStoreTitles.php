<?php

namespace App\Console\Commands;

use App\VariationInfo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class UpdateVariationInfoStoreTitles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'variation-info:update-store-titles
                            {--dry-run : Show what would be updated without making changes}
                            {--chunk=100 : Number of records to process at once}
                            {--create-reversion-log : Create a backup file for reverting changes}
                            {--backup-file= : Custom filename for the backup file (optional)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update store_title field for all VariationInfo records by joining meta field values with " - "';

    /**
     * Backup data for reversion
     *
     * @var array
     */
    protected $backupData = [];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $chunkSize = (int) $this->option('chunk');
        $createReversionLog = $this->option('create-reversion-log');
        $customBackupFile = $this->option('backup-file');

        $this->info('Starting VariationInfo store_title update process...');

        if ($dryRun) {
            $this->warn('DRY RUN MODE: No changes will be made to the database.');
        }

        // Setup backup file if requested
        $backupFilePath = null;
        if ($createReversionLog || $customBackupFile) {
            $backupFilePath = $this->setupBackupFile($customBackupFile, $dryRun);
            if (!$backupFilePath) {
                $this->error('Failed to setup backup file. Aborting operation for safety.');
                return 1;
            }
        }

        $totalRecords = VariationInfo::count();
        $this->info("Total VariationInfo records to process: {$totalRecords}");
        
        $processedCount = 0;
        $updatedCount = 0;
        $errorCount = 0;
        
        $progressBar = $this->output->createProgressBar($totalRecords);
        $progressBar->start();
        
        try {
            VariationInfo::latest()->chunk($chunkSize, function ($variationInfos) use ($dryRun, &$processedCount, &$updatedCount, &$errorCount, $progressBar, $backupFilePath) {
                foreach ($variationInfos as $variationInfo) {
                    try {
                        $processedCount++;

                        // Decode the meta field JSON
                        $metaData = json_decode($variationInfo->meta, true);

                        // Handle cases where meta might be null, empty, or invalid JSON
                        if (empty($metaData) || !is_array($metaData)) {
                            $newStoreTitle = '';
                        } else {
                            // Filter out empty/null values and join with " - "
                            $metaValues = array_filter($metaData, function ($value) {
                                return !is_null($value) && trim($value) !== '';
                            });

                            $newStoreTitle = implode(' - ', $metaValues);
                        }

                        // Only update if the store_title has changed
                        if ($variationInfo->store_title !== $newStoreTitle) {
                            // Store backup data if backup file is being created
                            if ($backupFilePath) {
                                $this->addToBackup($variationInfo, $newStoreTitle);
                            }

                            if (!$dryRun) {
                                $variationInfo->update(['store_title' => $newStoreTitle]);
                            }

                            $updatedCount++;

                            // Log the change for verification
                            $logMessage = sprintf(
                                'VariationInfo ID %d: Updated store_title from "%s" to "%s" (Meta: %s)',
                                $variationInfo->id,
                                $variationInfo->store_title ?? 'NULL',
                                $newStoreTitle,
                                $variationInfo->meta ?? 'NULL'
                            );

                            if ($dryRun) {
                                $this->line("\n[DRY RUN] " . $logMessage);
                            } else {
                                Log::info($logMessage);
                            }
                        }
                        
                    } catch (\Exception $e) {
                        $errorCount++;
                        $errorMessage = sprintf(
                            'Error processing VariationInfo ID %d: %s',
                            $variationInfo->id ?? 'unknown',
                            $e->getMessage()
                        );
                        
                        $this->error("\n" . $errorMessage);
                        Log::error($errorMessage, ['exception' => $e]);
                    }
                    
                    $progressBar->advance();
                }
            });
            
        } catch (\Exception $e) {
            $this->error("\nFatal error during processing: " . $e->getMessage());
            Log::error('Fatal error in UpdateVariationInfoStoreTitles command', ['exception' => $e]);
            return 1;
        }

        // Save backup file if we have backup data
        if ($backupFilePath && !empty($this->backupData)) {
            if (!$this->saveBackupFile($backupFilePath, $dryRun)) {
                $this->error('Failed to save backup file. Changes were made but backup is incomplete!');
                return 1;
            }
        }

        $progressBar->finish();
        $this->newLine(2);

        // Display summary
        $this->info('Update process completed!');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Records Processed', $processedCount],
                ['Records Updated', $updatedCount],
                ['Errors Encountered', $errorCount],
                ['Records Unchanged', $processedCount - $updatedCount - $errorCount],
            ]
        );

        if ($backupFilePath && !empty($this->backupData)) {
            $this->info("Backup file created: {$backupFilePath}");
            $this->info('Use the revert command to undo changes: php artisan variation-info:revert-store-titles --from-backup=' . basename($backupFilePath));
        }

        if ($dryRun) {
            $this->warn('This was a dry run. To apply changes, run the command without --dry-run option.');
        } else {
            $this->info('All changes have been saved to the database.');
            if ($updatedCount > 0) {
                $this->info('Check the application logs for detailed information about each update.');
            }
        }

        return $errorCount > 0 ? 1 : 0;
    }

    /**
     * Setup backup file path and ensure directory exists
     *
     * @param string|null $customFilename
     * @param bool $dryRun
     * @return string|null
     */
    protected function setupBackupFile($customFilename = null, $dryRun = false)
    {
        try {
            // Create backups directory if it doesn't exist
            $backupDir = 'backups/variation-info';
            if (!Storage::disk('local')->exists($backupDir)) {
                Storage::disk('local')->makeDirectory($backupDir);
            }

            // Generate filename
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $filename = $customFilename ?: "store_titles_backup_{$timestamp}.json";

            // Ensure .json extension
            if (!str_ends_with($filename, '.json')) {
                $filename .= '.json';
            }

            $backupPath = $backupDir . '/' . $filename;
            $fullPath = storage_path('app/' . $backupPath);

            if ($dryRun) {
                $this->info("Backup file would be created at: {$fullPath}");
            } else {
                $this->info("Backup file will be created at: {$fullPath}");
            }

            return $backupPath;

        } catch (\Exception $e) {
            $this->error("Failed to setup backup file: " . $e->getMessage());
            Log::error('Failed to setup backup file', ['exception' => $e]);
            return null;
        }
    }

    /**
     * Add record data to backup array
     *
     * @param VariationInfo $variationInfo
     * @param string $newStoreTitle
     */
    protected function addToBackup($variationInfo, $newStoreTitle)
    {
        $this->backupData[] = [
            'id' => $variationInfo->id,
            'original_store_title' => $variationInfo->store_title,
            'new_store_title' => $newStoreTitle,
            'original_meta' => $variationInfo->meta,
            'timestamp' => Carbon::now()->toISOString(),
        ];
    }

    /**
     * Save backup data to file
     *
     * @param string $backupPath
     * @param bool $dryRun
     * @return bool
     */
    protected function saveBackupFile($backupPath, $dryRun = false)
    {
        try {
            if ($dryRun) {
                $this->info("Would save " . count($this->backupData) . " records to backup file.");
                return true;
            }

            $backupContent = [
                'metadata' => [
                    'created_at' => Carbon::now()->toISOString(),
                    'command' => 'variation-info:update-store-titles',
                    'total_records' => count($this->backupData),
                    'laravel_version' => app()->version(),
                ],
                'records' => $this->backupData
            ];

            $success = Storage::disk('local')->put($backupPath, json_encode($backupContent, JSON_PRETTY_PRINT));

            if ($success) {
                $this->info("Successfully saved " . count($this->backupData) . " records to backup file.");
                return true;
            } else {
                $this->error("Failed to write backup file.");
                return false;
            }

        } catch (\Exception $e) {
            $this->error("Error saving backup file: " . $e->getMessage());
            Log::error('Error saving backup file', ['exception' => $e, 'backup_path' => $backupPath]);
            return false;
        }
    }
}
