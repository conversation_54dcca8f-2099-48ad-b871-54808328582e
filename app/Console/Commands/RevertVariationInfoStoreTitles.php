<?php

namespace App\Console\Commands;

use App\VariationInfo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class RevertVariationInfoStoreTitles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'variation-info:revert-store-titles 
                            {--from-backup= : Path to the backup file to restore from}
                            {--dry-run : Show what would be reverted without making changes}
                            {--list-backups : List available backup files}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Revert store_title field changes for VariationInfo records using a backup file';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $backupFile = $this->option('from-backup');
        $dryRun = $this->option('dry-run');
        $listBackups = $this->option('list-backups');

        if ($listBackups) {
            return $this->listAvailableBackups();
        }

        if (!$backupFile) {
            $this->error('Please specify a backup file using --from-backup option.');
            $this->info('Use --list-backups to see available backup files.');
            return 1;
        }

        $this->info('Starting VariationInfo store_title reversion process...');
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE: No changes will be made to the database.');
        }

        // Load and validate backup file
        $backupData = $this->loadBackupFile($backupFile);
        if (!$backupData) {
            return 1;
        }

        $records = $backupData['records'] ?? [];
        $totalRecords = count($records);
        
        if ($totalRecords === 0) {
            $this->warn('No records found in backup file.');
            return 0;
        }

        $this->info("Found {$totalRecords} records to revert from backup file.");
        $this->info("Backup created: " . ($backupData['metadata']['created_at'] ?? 'Unknown'));

        if (!$dryRun && !$this->confirm('Are you sure you want to revert these changes?')) {
            $this->info('Reversion cancelled.');
            return 0;
        }

        $processedCount = 0;
        $revertedCount = 0;
        $errorCount = 0;
        $notFoundCount = 0;

        $progressBar = $this->output->createProgressBar($totalRecords);
        $progressBar->start();

        try {
            DB::transaction(function () use ($records, $dryRun, &$processedCount, &$revertedCount, &$errorCount, &$notFoundCount, $progressBar) {
                foreach ($records as $record) {
                    try {
                        $processedCount++;
                        
                        $variationInfo = VariationInfo::find($record['id']);
                        
                        if (!$variationInfo) {
                            $notFoundCount++;
                            $this->warn("\nVariationInfo ID {$record['id']} not found in database.");
                            $progressBar->advance();
                            continue;
                        }

                        // Check if current store_title matches what we expect
                        $currentTitle = $variationInfo->store_title;
                        $expectedTitle = $record['new_store_title'];
                        $originalTitle = $record['original_store_title'];

                        if ($currentTitle !== $expectedTitle) {
                            $this->warn("\nWarning: VariationInfo ID {$record['id']} has unexpected store_title. Expected: '{$expectedTitle}', Found: '{$currentTitle}'");
                        }

                        if (!$dryRun) {
                            $variationInfo->update(['store_title' => $originalTitle]);
                        }

                        $revertedCount++;

                        $logMessage = sprintf(
                            'VariationInfo ID %d: Reverted store_title from "%s" to "%s"',
                            $record['id'],
                            $currentTitle ?? 'NULL',
                            $originalTitle ?? 'NULL'
                        );

                        if ($dryRun) {
                            $this->line("\n[DRY RUN] " . $logMessage);
                        } else {
                            Log::info($logMessage);
                        }

                    } catch (\Exception $e) {
                        $errorCount++;
                        $errorMessage = sprintf(
                            'Error reverting VariationInfo ID %d: %s',
                            $record['id'] ?? 'unknown',
                            $e->getMessage()
                        );
                        
                        $this->error("\n" . $errorMessage);
                        Log::error($errorMessage, ['exception' => $e]);
                    }
                    
                    $progressBar->advance();
                }
            });

        } catch (\Exception $e) {
            $this->error("\nFatal error during reversion: " . $e->getMessage());
            Log::error('Fatal error in RevertVariationInfoStoreTitles command', ['exception' => $e]);
            return 1;
        }

        $progressBar->finish();
        $this->newLine(2);

        // Display summary
        $this->info('Reversion process completed!');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Records Processed', $processedCount],
                ['Records Reverted', $revertedCount],
                ['Records Not Found', $notFoundCount],
                ['Errors Encountered', $errorCount],
            ]
        );

        if ($dryRun) {
            $this->warn('This was a dry run. To apply changes, run the command without --dry-run option.');
        } else {
            $this->info('All changes have been reverted in the database.');
            if ($revertedCount > 0) {
                $this->info('Check the application logs for detailed information about each reversion.');
            }
        }

        return $errorCount > 0 ? 1 : 0;
    }

    /**
     * Load and validate backup file
     *
     * @param string $backupFile
     * @return array|null
     */
    protected function loadBackupFile($backupFile)
    {
        try {
            // Handle both full paths and just filenames
            if (!str_contains($backupFile, '/')) {
                $backupFile = 'backups/variation-info/' . $backupFile;
            }

            if (!Storage::disk('local')->exists($backupFile)) {
                $this->error("Backup file not found: {$backupFile}");
                $this->info('Use --list-backups to see available backup files.');
                return null;
            }

            $content = Storage::disk('local')->get($backupFile);
            $data = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->error('Invalid JSON in backup file: ' . json_last_error_msg());
                return null;
            }

            if (!isset($data['records']) || !is_array($data['records'])) {
                $this->error('Invalid backup file format: missing or invalid records array.');
                return null;
            }

            $this->info("Successfully loaded backup file: {$backupFile}");
            return $data;

        } catch (\Exception $e) {
            $this->error("Error loading backup file: " . $e->getMessage());
            Log::error('Error loading backup file', ['exception' => $e, 'backup_file' => $backupFile]);
            return null;
        }
    }

    /**
     * List available backup files
     *
     * @return int
     */
    protected function listAvailableBackups()
    {
        try {
            $backupDir = 'backups/variation-info';
            
            if (!Storage::disk('local')->exists($backupDir)) {
                $this->info('No backup directory found. No backups available.');
                return 0;
            }

            $files = Storage::disk('local')->files($backupDir);
            $backupFiles = array_filter($files, function ($file) {
                return str_ends_with($file, '.json');
            });

            if (empty($backupFiles)) {
                $this->info('No backup files found.');
                return 0;
            }

            $this->info('Available backup files:');
            $this->newLine();

            $tableData = [];
            foreach ($backupFiles as $file) {
                $filename = basename($file);
                $fullPath = storage_path('app/' . $file);
                $size = Storage::disk('local')->size($file);
                $modified = Carbon::createFromTimestamp(Storage::disk('local')->lastModified($file));

                // Try to get metadata from file
                $recordCount = 'Unknown';
                try {
                    $content = Storage::disk('local')->get($file);
                    $data = json_decode($content, true);
                    if (isset($data['metadata']['total_records'])) {
                        $recordCount = $data['metadata']['total_records'];
                    } elseif (isset($data['records'])) {
                        $recordCount = count($data['records']);
                    }
                } catch (\Exception $e) {
                    // Ignore errors when reading metadata
                }

                $tableData[] = [
                    $filename,
                    $this->formatBytes($size),
                    $modified->format('Y-m-d H:i:s'),
                    $recordCount,
                ];
            }

            $this->table(
                ['Filename', 'Size', 'Created', 'Records'],
                $tableData
            );

            $this->newLine();
            $this->info('To revert using a backup file, run:');
            $this->info('php artisan variation-info:revert-store-titles --from-backup=FILENAME');

            return 0;

        } catch (\Exception $e) {
            $this->error("Error listing backup files: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Format bytes to human readable format
     *
     * @param int $bytes
     * @return string
     */
    protected function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
