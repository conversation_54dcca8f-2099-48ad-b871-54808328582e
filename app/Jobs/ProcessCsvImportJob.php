<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Capitalc\CustomCsvImport\CustomImporter;
use Laravel\Nova\Resource;
use Exception;

class ProcessCsvImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 3600; // 1 hour timeout for large imports

    protected $filePath;
    protected $resourceClass;
    protected $modelClass;
    protected $attributeMap;
    protected $rules;
    protected $meta;
    protected $randomStringSettings;
    protected $customValues;
    protected $combinedValues;
    protected $resultsPath;

    /**
     * Create a new job instance.
     */
    public function __construct(
        string $filePath,
        string $resourceClass,
        string $modelClass,
        array $attributeMap = [],
        array $rules = [],
        array $meta = [],
        array $randomStringSettings = [],
        array $customValues = [],
        array $combinedValues = []
    ) {
        $this->filePath = $filePath;
        $this->resourceClass = $resourceClass;
        $this->modelClass = $modelClass;
        $this->attributeMap = $attributeMap;
        $this->rules = $rules;
        $this->meta = $meta;
        $this->randomStringSettings = $randomStringSettings;
        $this->customValues = $customValues;
        $this->combinedValues = $combinedValues;
        
        // Generate results file path
        // Extract hash from the file path (remove directory and extension)
        $filename = basename($filePath);
        $hash = pathinfo($filename, PATHINFO_FILENAME);
        $this->resultsPath = "csv-import/{$hash}.csv.results.json";

        // Use default queue for background processing
        $this->onQueue('default');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting CSV import job', [
                'file' => $this->filePath,
                'resource' => $this->resourceClass,
                'model' => $this->modelClass
            ]);

            // Initialize results structure
            $results = [
                'status' => 'processing',
                'started_at' => now()->toISOString(),
                'completed_at' => null,
                'total_rows' => 0,
                'processed_rows' => 0,
                'successful_rows' => 0,
                'failed_rows' => 0,
                'failures' => [],
                'errors' => []
            ];

            // Save initial status
            $this->saveResults($results);

            // Set up the CustomImporter with all configuration
            $importer = new CustomImporter();
            $resource = new $this->resourceClass(new $this->modelClass());
            
            $importer->setResource($resource);
            $importer->setModelClass($this->modelClass);
            $importer->setAttributeMap($this->attributeMap);
            $importer->setRules($this->rules);
            $importer->setMeta($this->meta);
            $importer->setRandomStringSettings($this->randomStringSettings);
            $importer->setCustomValues($this->customValues);
            $importer->setCombinedValues($this->combinedValues);

            // Get full file path
            $fullFilePath = Storage::path($this->filePath);
            
            if (!file_exists($fullFilePath)) {
                throw new Exception("CSV file not found: {$fullFilePath}");
            }

            // Count total rows for progress tracking
            $totalRows = $this->countCsvRows($fullFilePath);
            $results['total_rows'] = $totalRows;
            $this->saveResults($results);

            Log::info('CSV import processing started', [
                'total_rows' => $totalRows,
                'file_path' => $fullFilePath
            ]);

            // Process the import using our CustomImporter with progress tracking
            $this->processImportWithProgress($importer, $fullFilePath, $totalRows);

            // Get failures from the importer
            $failures = $importer->failures();
            $failureData = [];

            foreach ($failures as $failure) {
                $failureData[] = [
                    'row' => $failure->row(),
                    'attribute' => $failure->attribute(),
                    'errors' => $failure->errors(),
                    'values' => $failure->values()
                ];
            }

            // Get current progress from results file
            $currentResults = json_decode(Storage::get($this->resultsPath), true);

            // Update final results (include 'imported' key for base controller compatibility)
            $results = [
                'status' => 'completed',
                'started_at' => $currentResults['started_at'],
                'completed_at' => now()->toISOString(),
                'total_rows' => $totalRows,
                'processed_rows' => $currentResults['processed_rows'],
                'successful_rows' => $currentResults['successful_rows'],
                'failed_rows' => $currentResults['failed_rows'],
                'imported' => $currentResults['successful_rows'], // ✅ Add imported key for base controller
                'failures' => $failureData,
                'errors' => []
            ];

            $this->saveResults($results);

            Log::info('CSV import job completed successfully', [
                'total_rows' => $totalRows,
                'successful_rows' => $results['successful_rows'],
                'failed_rows' => $results['failed_rows']
            ]);

        } catch (Exception $e) {
            Log::error('CSV import job failed', [
                'error' => $e->getMessage(),
                'file' => $this->filePath,
                'trace' => $e->getTraceAsString()
            ]);

            // Save error results (include 'imported' key for base controller compatibility)
            $errorResults = [
                'status' => 'failed',
                'started_at' => $results['started_at'] ?? now()->toISOString(),
                'completed_at' => now()->toISOString(),
                'total_rows' => $results['total_rows'] ?? 0,
                'processed_rows' => $results['processed_rows'] ?? 0,
                'successful_rows' => 0,
                'failed_rows' => $results['total_rows'] ?? 0,
                'imported' => 0, // ✅ Add imported key for base controller (0 for failed jobs)
                'failures' => [],
                'errors' => [
                    [
                        'message' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine()
                    ]
                ]
            ];

            $this->saveResults($errorResults);
            
            // Re-throw to mark job as failed
            throw $e;
        }
    }

    /**
     * Count the number of rows in the CSV file (excluding header)
     */
    private function countCsvRows(string $filePath): int
    {
        $count = 0;
        $handle = fopen($filePath, 'r');
        
        if ($handle) {
            // Skip header row
            fgetcsv($handle);
            
            while (fgetcsv($handle) !== false) {
                $count++;
            }
            
            fclose($handle);
        }
        
        return $count;
    }

    /**
     * Process the import with progress tracking
     */
    private function processImportWithProgress($importer, string $filePath, int $totalRows): void
    {
        $handle = fopen($filePath, 'r');
        if (!$handle) {
            throw new Exception("Could not open CSV file: {$filePath}");
        }

        // Skip header row
        $header = fgetcsv($handle);

        $processedRows = 0;
        $successfulRows = 0;
        $failedRows = 0;

        // Process rows in batches for progress updates
        while (($row = fgetcsv($handle)) !== false) {
            if (empty(array_filter($row))) {
                continue; // Skip empty rows
            }

            // Convert row to associative array using header
            $rowData = array_combine($header, $row);

            try {
                // Process individual row using our CustomImporter
                $model = $importer->model($rowData);

                // If model has attributes, attempt to save it
                if ($model && count($model->getAttributes()) > 0) {
                    $model->save();
                    $successfulRows++;
                } else {
                    $failedRows++;
                }
            } catch (Exception $e) {
                $failedRows++;
                Log::warning('Row processing failed', [
                    'row' => $processedRows + 1,
                    'data' => $rowData,
                    'error' => $e->getMessage()
                ]);
            }

            $processedRows++;

            // Update progress every 10 rows or on last row
            if ($processedRows % 10 === 0 || $processedRows === $totalRows) {
                $this->updateProgress($totalRows, $processedRows, $successfulRows, $failedRows);
            }
        }

        fclose($handle);

        // Final progress update
        $this->updateProgress($totalRows, $processedRows, $successfulRows, $failedRows);
    }

    /**
     * Update progress in the results file
     */
    private function updateProgress(int $totalRows, int $processedRows, int $successfulRows, int $failedRows): void
    {
        $results = [
            'status' => 'processing',
            'started_at' => $this->getStartTime(),
            'completed_at' => null,
            'total_rows' => $totalRows,
            'processed_rows' => $processedRows,
            'successful_rows' => $successfulRows,
            'failed_rows' => $failedRows,
            'failures' => [], // Will be populated at the end
            'errors' => []
        ];

        $this->saveResults($results);
    }

    /**
     * Get the start time from existing results or current time
     */
    private function getStartTime(): string
    {
        if (Storage::exists($this->resultsPath)) {
            $existingResults = json_decode(Storage::get($this->resultsPath), true);
            return $existingResults['started_at'] ?? now()->toISOString();
        }

        return now()->toISOString();
    }

    /**
     * Save results to the JSON file
     */
    private function saveResults(array $results): void
    {
        Storage::put($this->resultsPath, json_encode($results, JSON_PRETTY_PRINT));
    }

    /**
     * Handle job failure
     */
    public function failed(Exception $exception): void
    {
        Log::error('CSV import job failed permanently', [
            'error' => $exception->getMessage(),
            'file' => $this->filePath,
            'trace' => $exception->getTraceAsString()
        ]);

        // Ensure error results are saved (include 'imported' key for base controller compatibility)
        $errorResults = [
            'status' => 'failed',
            'started_at' => now()->toISOString(),
            'completed_at' => now()->toISOString(),
            'total_rows' => 0,
            'processed_rows' => 0,
            'successful_rows' => 0,
            'failed_rows' => 0,
            'imported' => 0, // ✅ Add imported key for base controller (0 for failed jobs)
            'failures' => [],
            'errors' => [
                [
                    'message' => $exception->getMessage(),
                    'file' => $exception->getFile(),
                    'line' => $exception->getLine()
                ]
            ]
        ];

        $this->saveResults($errorResults);
    }
}
